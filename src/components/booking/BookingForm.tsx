import { useState } from "react";
import { BookingProgress } from "./BookingProgress";
import { LocationStep } from "./steps/LocationStep";
import { ServiceStep } from "./steps/ServiceStep";
import { ScheduleStep } from "./steps/ScheduleStep";
import { ContactStep } from "./steps/ContactStep";
import { ConfirmationStep } from "./steps/ConfirmationStep";
import { toast } from "@/hooks/use-toast";
import { CheckCircle } from "lucide-react";

interface BookingData {
  zipCode?: string;
  service?: string;
  description?: string;
  date?: Date;
  time?: string;
  firstName?: string;
  lastName?: string;
  email?: string;
  phone?: string;
  address?: string;
  notes?: string;
}

const STEPS = ["Location", "Service", "Schedule", "Contact", "Confirm"];

export function BookingForm() {
  const [currentStep, setCurrentStep] = useState(1);
  const [bookingData, setBookingData] = useState<BookingData>({});
  const [isComplete, setIsComplete] = useState(false);

  const updateBookingData = (stepData: Partial<BookingData>) => {
    setBookingData(prev => ({ ...prev, ...stepData }));
  };

  const goToNextStep = (stepData: Partial<BookingData>) => {
    updateBookingData(stepData);
    if (currentStep < STEPS.length) {
      setCurrentStep(prev => prev + 1);
    }
  };

  const goToPreviousStep = () => {
    if (currentStep > 1) {
      setCurrentStep(prev => prev - 1);
    }
  };

  const handleConfirmBooking = () => {
    setIsComplete(true);
    toast({
      title: "Booking Confirmed!",
      description: "Your appointment has been successfully scheduled. You'll receive a confirmation email shortly.",
    });
  };

  const handleStartNewBooking = () => {
    setCurrentStep(1);
    setBookingData({});
    setIsComplete(false);
  };

  if (isComplete) {
    return (
      <div className="min-h-screen bg-gradient-subtle flex items-center justify-center p-4">
        <div className="w-full max-w-md text-center">
          <div className="w-20 h-20 bg-booking-step-complete rounded-full flex items-center justify-center mx-auto mb-6">
            <CheckCircle className="w-10 h-10 text-white" />
          </div>
          <h1 className="text-3xl font-bold text-foreground mb-4">
            Booking Confirmed!
          </h1>
          <p className="text-muted-foreground mb-8">
            Thank you for choosing our services. We'll be in touch shortly to confirm your appointment details.
          </p>
          <button
            onClick={handleStartNewBooking}
            className="w-full bg-gradient-primary text-white py-3 px-6 rounded-lg font-medium hover:opacity-90 transition-opacity"
          >
            Book Another Appointment
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-subtle">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-foreground mb-2">
            Book Your Service
          </h1>
          <p className="text-muted-foreground">
            Schedule your appointment in just a few easy steps
          </p>
        </div>

        {/* Progress Indicator */}
        <div className="max-w-4xl mx-auto mb-8">
          <BookingProgress 
            currentStep={currentStep} 
            totalSteps={STEPS.length} 
            steps={STEPS}
          />
        </div>

        {/* Form Steps */}
        <div className="max-w-4xl mx-auto bg-gradient-card rounded-xl shadow-lg p-8">
          {currentStep === 1 && (
            <LocationStep 
              onNext={goToNextStep}
              initialData={{ zipCode: bookingData.zipCode || "" }}
            />
          )}
          
          {currentStep === 2 && (
            <ServiceStep 
              onNext={goToNextStep}
              onBack={goToPreviousStep}
              initialData={{ 
                service: bookingData.service || "", 
                description: bookingData.description || "" 
              }}
            />
          )}
          
          {currentStep === 3 && (
            <ScheduleStep 
              onNext={goToNextStep}
              onBack={goToPreviousStep}
              initialData={{ 
                date: bookingData.date, 
                time: bookingData.time || "" 
              }}
            />
          )}
          
          {currentStep === 4 && (
            <ContactStep 
              onNext={goToNextStep}
              onBack={goToPreviousStep}
              initialData={{
                firstName: bookingData.firstName,
                lastName: bookingData.lastName,
                email: bookingData.email,
                phone: bookingData.phone,
                address: bookingData.address,
                notes: bookingData.notes,
              }}
            />
          )}
          
          {currentStep === 5 && bookingData.zipCode && bookingData.service && bookingData.date && bookingData.time && (
            <ConfirmationStep 
              data={bookingData as any} // Type assertion since we've checked required fields
              onBack={goToPreviousStep}
              onConfirm={handleConfirmBooking}
            />
          )}
        </div>
      </div>
    </div>
  );
}