import { useState } from "react";
import { Calendar, Clock } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Calendar as CalendarComponent } from "@/components/ui/calendar";
import { cn } from "@/lib/utils";
import { format } from "date-fns";

interface ScheduleStepProps {
  onNext: (data: { date: Date; time: string }) => void;
  onBack: () => void;
  initialData?: { date: Date; time: string };
}

const timeSlots = [
  "8:00 AM - 10:00 AM",
  "10:00 AM - 12:00 PM",
  "12:00 PM - 2:00 PM",
  "2:00 PM - 4:00 PM",
  "4:00 PM - 6:00 PM",
];

export function ScheduleStep({ onNext, onBack, initialData }: ScheduleStepProps) {
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(initialData?.date);
  const [selectedTime, setSelectedTime] = useState(initialData?.time || "");

  const handleNext = () => {
    if (!selectedDate || !selectedTime) return;
    
    onNext({
      date: selectedDate,
      time: selectedTime,
    });
  };

  // Disable past dates
  const disabledDays = (date: Date) => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    return date < today;
  };

  return (
    <div className="max-w-2xl mx-auto">
      <div className="text-center mb-8">
        <div className="w-16 h-16 bg-accent rounded-full flex items-center justify-center mx-auto mb-4">
          <Calendar className="w-8 h-8 text-primary" />
        </div>
        <h2 className="text-2xl font-bold text-foreground mb-3">
          Schedule your appointment
        </h2>
        <p className="text-muted-foreground">
          Choose your preferred date and time for the service.
        </p>
      </div>

      <div className="grid md:grid-cols-2 gap-8 mb-8">
        {/* Calendar */}
        <div>
          <h3 className="font-semibold text-foreground mb-4 flex items-center gap-2">
            <Calendar className="w-5 h-5" />
            Select Date
          </h3>
          <div className="border border-border rounded-lg p-4 bg-booking-card">
            <CalendarComponent
              mode="single"
              selected={selectedDate}
              onSelect={setSelectedDate}
              disabled={disabledDays}
              className={cn("p-0 pointer-events-auto")}
            />
          </div>
          {selectedDate && (
            <p className="text-sm text-primary font-medium mt-2">
              Selected: {format(selectedDate, "EEEE, MMMM do, yyyy")}
            </p>
          )}
        </div>

        {/* Time Slots */}
        <div>
          <h3 className="font-semibold text-foreground mb-4 flex items-center gap-2">
            <Clock className="w-5 h-5" />
            Select Time
          </h3>
          <div className="mb-4"> {/* New div for grouping messages */}
            <p className="text-sm text-muted-foreground mb-1"> {/* Adjusted mb */}
              The service person will visit anytime within the selected time range.
            </p>
          </div>
          <div className="grid grid-cols-2 gap-2 max-h-80 overflow-y-auto">
            {timeSlots.map((time) => (
              <button
                key={time}
                onClick={() => setSelectedTime(time)}
                disabled={!selectedDate}
                className={cn(
                  "p-3 text-sm font-medium rounded-lg border-2 transition-all duration-200",
                  !selectedDate
                    ? "border-border bg-muted text-muted-foreground cursor-not-allowed"
                    : selectedTime === time
                    ? "border-primary bg-accent text-primary"
                    : "border-border bg-booking-card text-foreground hover:border-primary/30 hover:bg-accent/50"
                )}
              >
                {time}
              </button>
            ))}
          </div>
          {selectedTime && ( // Conditional rendering for the second message
            <p className="text-xs text-muted-foreground mt-4"> {/* Added mt-4 for spacing */}
              Please note: The actual service duration may vary and could extend beyond the selected time range.
            </p>
          )}
        </div>
      </div>

      <div className="flex gap-4">
        <Button variant="outline" onClick={onBack} className="flex-1">
          Back
        </Button>
        <Button
          onClick={handleNext}
          disabled={!selectedDate || !selectedTime}
          className="flex-1 bg-gradient-primary hover:opacity-90 transition-opacity"
        >
          Continue
        </Button>
      </div>
    </div>
  );
}