import { Check<PERSON>ir<PERSON>, Calendar, Clock, MapPin, User, Wrench, Mail, Phone } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { format } from "date-fns";

interface ConfirmationData {
  zipCode: string;
  service: string;
  description: string;
  date: Date;
  time: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  address: string;
  notes: string;
}

interface ConfirmationStepProps {
  data: ConfirmationData;
  onBack: () => void;
  onConfirm: () => void;
}

const serviceNames: Record<string, string> = {
  plumbing: "Plumbing",
  electrical: "Electrical",
  hvac: "HVAC",
  "water-heater": "Water Heater",
  general: "General Repair",
};

export function ConfirmationStep({ data, onBack, onConfirm }: ConfirmationStepProps) {
  return (
    <div className="max-w-2xl mx-auto">
      <div className="text-center mb-8">
        <div className="w-16 h-16 bg-accent rounded-full flex items-center justify-center mx-auto mb-4">
          <CheckCircle className="w-8 h-8 text-booking-step-complete" />
        </div>
        <h2 className="text-2xl font-bold text-foreground mb-3">
          Confirm Your Appointment
        </h2>
        <p className="text-muted-foreground">
          Please review your booking details before confirming.
        </p>
      </div>

      <div className="space-y-6 mb-8">
        {/* Service Information */}
        <Card className="border-l-4 border-l-primary">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center gap-2">
              <Wrench className="w-5 h-5 text-primary" />
              Service Details
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex justify-between items-start">
              <span className="text-muted-foreground">Service Type:</span>
              <span className="font-medium text-right">{serviceNames[data.service]}</span>
            </div>
            {data.description && (
              <div className="flex justify-between items-start">
                <span className="text-muted-foreground">Description:</span>
                <span className="font-medium text-right max-w-xs">{data.description}</span>
              </div>
            )}
            <div className="flex justify-between items-start">
              <span className="text-muted-foreground">Service Area:</span>
              <span className="font-medium">{data.zipCode}</span>
            </div>
          </CardContent>
        </Card>

        {/* Schedule Information */}
        <Card className="border-l-4 border-l-primary">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center gap-2">
              <Calendar className="w-5 h-5 text-primary" />
              Schedule
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-muted-foreground flex items-center gap-2">
                <Calendar className="w-4 h-4" />
                Date:
              </span>
              <span className="font-medium">{format(data.date, "EEEE, MMMM do, yyyy")}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-muted-foreground flex items-center gap-2">
                <Clock className="w-4 h-4" />
                Time:
              </span>
              <span className="font-medium">{data.time}</span>
            </div>
          </CardContent>
        </Card>

        {/* Contact Information */}
        <Card className="border-l-4 border-l-primary">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center gap-2">
              <User className="w-5 h-5 text-primary" />
              Contact Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-muted-foreground">Name:</span>
              <span className="font-medium">{data.firstName} {data.lastName}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-muted-foreground flex items-center gap-2">
                <Mail className="w-4 h-4" />
                Email:
              </span>
              <span className="font-medium">{data.email}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-muted-foreground flex items-center gap-2">
                <Phone className="w-4 h-4" />
                Phone:
              </span>
              <span className="font-medium">{data.phone}</span>
            </div>
            <div className="flex justify-between items-start">
              <span className="text-muted-foreground flex items-center gap-2">
                <MapPin className="w-4 h-4" />
                Address:
              </span>
              <span className="font-medium text-right max-w-xs">{data.address}</span>
            </div>
            {data.notes && (
              <div>
                <Separator className="my-3" />
                <div className="flex justify-between items-start">
                  <span className="text-muted-foreground">Notes:</span>
                  <span className="font-medium text-right max-w-xs">{data.notes}</span>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      <div className="bg-accent rounded-lg p-6 mb-8">
        <h3 className="font-semibold text-foreground mb-2">What happens next?</h3>
        <ul className="space-y-2 text-sm text-muted-foreground">
          <li className="flex items-start gap-2">
            <CheckCircle className="w-4 h-4 text-booking-step-complete mt-0.5 flex-shrink-0" />
            You'll receive a confirmation email shortly
          </li>
          <li className="flex items-start gap-2">
            <CheckCircle className="w-4 h-4 text-booking-step-complete mt-0.5 flex-shrink-0" />
            Our team will call you to confirm the appointment details
          </li>
          <li className="flex items-start gap-2">
            <CheckCircle className="w-4 h-4 text-booking-step-complete mt-0.5 flex-shrink-0" />
            A qualified technician will arrive at your scheduled time
          </li>
        </ul>
      </div>

      <div className="flex gap-4">
        <Button variant="outline" onClick={onBack} className="flex-1">
          Back
        </Button>
        <Button 
          onClick={onConfirm}
          className="flex-1 bg-gradient-primary hover:opacity-90 transition-opacity"
          size="lg"
        >
          Confirm Appointment
        </Button>
      </div>
    </div>
  );
}