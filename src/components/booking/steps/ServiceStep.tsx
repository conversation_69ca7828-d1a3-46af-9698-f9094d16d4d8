import { useState } from "react";
import { Wrench, Zap, Droplets, Wind, Thermometer } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { cn } from "@/lib/utils";

interface ServiceStepProps {
  onNext: (data: { service: string; description: string }) => void;
  onBack: () => void;
  initialData?: { service: string; description: string };
}

const services = {
  Plumbing: {
    Repair: [
      "Find & repair leak",
      "Repair faucet",
      "Repair garbage disposal",
      "Repair outdoor systems",
      "Repair pipe",
      "Repair sewer",
      "Repair shower",
      "Repair toilet",
      "Repair water heater",
      "Unclog drain",
    ],
    Install: [
      "Install faucet",
      "Install garbage disposal",
      "Install shower",
      "Install toilet",
      "Install water heater",
    ],
    Other: [],
  },
  "Heating & Cooling": {
    Repair: [
      "Ductless heating and AC services",
      "Repair AC",
      "Repair HVAC",
      "Repair ducts & vents",
      "Repair heating system",
      "Repair thermostat",
    ],
    Install: ["Install AC", "Install ducts & vents", "Install heating system", "Install thermostat"],
    Maintenance: ["AC maintenance", "HVAC maintenance", "Heating maintenance"],
    Other: [],
  },
  Electrical: {
    Repair: ["Repair fan", "Repair light fixtures", "Repair outlets or switches", "Repair panel", "Restore power"],
    Install: [
      "Install electric car charger",
      "Install fan",
      "Install ground wire",
      "Install light fixtures",
      "Install outdoor lighting",
      "Install outlets or switches",
      "Install security system",
      "Relocate outlets or switches",
      "Remodeling",
      "Replace or upgrade panel",
    ],
    Other: [],
  },
};

const serviceIcons = {
  Plumbing: Droplets,
  "Heating & Cooling": Wind,
  Electrical: Zap,
};

const serviceColors = {
  Plumbing: "text-blue-600",
  "Heating & Cooling": "text-green-600",
  Electrical: "text-yellow-600",
};

export function ServiceStep({ onNext, onBack, initialData }: ServiceStepProps) {
  const [selectedService, setSelectedService] = useState(initialData?.service || "");
  const [selectedSubService, setSelectedSubService] = useState("");
  const [selectedSpecificService, setSelectedSpecificService] = useState("");
  const [description, setDescription] = useState(initialData?.description || "");

  const handleServiceSelect = (serviceName: string) => {
    setSelectedService(serviceName);
    setSelectedSubService("");
    setSelectedSpecificService("");
  };

  const handleSubServiceSelect = (subServiceType: string) => {
    setSelectedSubService(subServiceType);
    setSelectedSpecificService("");
  };

  const handleNext = () => {
    if (!selectedService) return;
    
    let finalService = selectedService;
    if (selectedSubService && selectedSpecificService) {
      finalService = `${selectedService} - ${selectedSubService} - ${selectedSpecificService}`;
    } else if (selectedSubService) {
      finalService = `${selectedService} - ${selectedSubService}`;
    }
    
    onNext({
      service: finalService,
      description: description.trim(),
    });
  };

  const canContinue = () => {
    if (!selectedService) return false;
    const subServices = services[selectedService as keyof typeof services];
    if (Object.keys(subServices).length > 0 && !selectedSubService) return false;
    
    const specificOptions = subServices[selectedSubService as keyof typeof subServices];
    if (Array.isArray(specificOptions) && specificOptions.length > 0 && !selectedSpecificService) return false;

    return true;
  };

  return (
    <div className="max-w-2xl mx-auto">
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold text-foreground mb-3">
          How can we improve your comfort today?
        </h2>
        <p className="text-muted-foreground">
          Select the type of service you need assistance with.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
        {Object.keys(services).map((serviceName) => {
          const Icon = serviceIcons[serviceName as keyof typeof serviceIcons];
          const isSelected = selectedService === serviceName;
          
          return (
            <Card
              key={serviceName}
              className={cn(
                "cursor-pointer transition-all duration-200 hover:shadow-md border-2",
                isSelected
                  ? "border-primary bg-accent"
                  : "border-border hover:border-primary/30"
              )}
              onClick={() => handleServiceSelect(serviceName)}
            >
              <CardContent className="p-6 text-center">
                <div className={cn("p-3 rounded-lg bg-background mx-auto mb-3 w-fit", serviceColors[serviceName as keyof typeof serviceColors])}>
                  <Icon className="w-6 h-6" />
                </div>
                <h3 className="font-semibold text-foreground mb-1">
                  {serviceName}
                </h3>
                <p className="text-xs text-muted-foreground">
                  {/* You can add descriptions here if needed, or remove this line */}
                </p>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {selectedService && Object.keys(services[selectedService as keyof typeof services]).length > 0 && (
        <div className="mb-8">
          <h3 className="text-lg font-semibold text-foreground mb-4">
            Please select your service type
          </h3>
          <div className="flex gap-2 mb-6">
            {Object.keys(services[selectedService as keyof typeof services]).map((subServiceType) => (
              <Button
                key={subServiceType}
                variant={selectedSubService === subServiceType ? "default" : "outline"}
                onClick={() => handleSubServiceSelect(subServiceType)}
              >
                {subServiceType}
              </Button>
            ))}
          </div>

          {selectedSubService && selectedSubService !== "Other" && Array.isArray(services[selectedService as keyof typeof services][selectedSubService as keyof typeof services[keyof typeof services]]) && (
            <div>
              <h4 className="text-md font-semibold text-foreground mb-4">
                What needs to be {selectedSubService.toLowerCase()}?
              </h4>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                {(services[selectedService as keyof typeof services][selectedSubService as keyof typeof services[keyof typeof services]] as string[]).map((option) => (
                  <Button
                    key={option}
                    variant={selectedSpecificService === option ? "default" : "outline"}
                    onClick={() => setSelectedSpecificService(option)}
                    size="sm"
                    className="text-xs py-2 px-3"
                  >
                    {option}
                  </Button>
                ))}
              </div>
            </div>
          )}
        </div>
      )}

      <div className="mb-8">
        <label className="block text-sm font-medium text-foreground mb-2">
          Describe your issue (optional)
        </label>
        <textarea
          value={description}
          onChange={(e) => setDescription(e.target.value)}
          placeholder="Tell us more about what you need help with..."
          className="w-full min-h-24 p-3 border border-input rounded-lg bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring"
          rows={3}
        />
      </div>

      <div className="flex gap-4">
        <Button variant="outline" onClick={onBack} className="flex-1">
          Back
        </Button>
        <Button
          onClick={handleNext}
          disabled={!canContinue()}
          className="flex-1 bg-gradient-primary hover:opacity-90 transition-opacity"
        >
          Continue Booking
        </Button>
      </div>
    </div>
  );
}