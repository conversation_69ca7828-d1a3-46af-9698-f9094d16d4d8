import { useState } from "react";
import { MapPinned, Truck } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

interface LocationStepProps {
  onNext: (data: { zipCode: string }) => void;
  initialData?: { zipCode: string };
}

export function LocationStep({ onNext, initialData }: LocationStepProps) {
  const [zipCode, setZipCode] = useState(initialData?.zipCode || "");
  const [error, setError] = useState("");

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!zipCode.trim()) {
      setError("Please enter your zip code");
      return;
    }

    setError("");
    onNext({ zipCode: zipCode.trim() });
  };
  return (
    <div className="text-center max-w-md mx-auto">
      <div className="mb-8">
        <div className="w-20 h-20 bg-accent rounded-full flex items-center justify-center mx-auto mb-6">
          <div className="relative">
            <MapPinned
              strokeWidth={}
              className="w-16 h-16 text-primary absolute -bottom-5 -right-8"
            />
            {/* <Truck
              className="w-16 h-16 text-primary-light absolute -bottom-8 -right-8"
              strokeWidth={1.2}
            /> */}
          </div>
        </div>

        <h2 className="text-2xl font-bold text-foreground mb-3">
          Where are you?
        </h2>
        <p className="text-muted-foreground">
          Enter your zip or postal code so we can check if we provide service in
          your area.
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="text-left">
          <Label htmlFor="zipCode" className="text-sm font-medium">
            Zip Code <span className="text-destructive">*</span>
          </Label>
          <Input
            id="zipCode"
            type="text"
            placeholder="Enter zip code"
            value={zipCode}
            onChange={(e) => {
              setZipCode(e.target.value);
              setError("");
            }}
            className={`mt-2 text-center ${error ? "border-destructive" : ""}`}
            maxLength={10}
          />
          {error && <p className="text-destructive text-sm mt-1">{error}</p>}
        </div>

        <Button
          type="submit"
          className="w-full bg-gradient-primary hover:opacity-90 transition-opacity"
          size="lg"
        >
          Confirm
        </Button>
      </form>
    </div>
  );
}
