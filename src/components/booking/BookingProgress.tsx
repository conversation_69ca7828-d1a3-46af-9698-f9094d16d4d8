import { Check } from "lucide-react";
import { cn } from "@/lib/utils";

interface BookingProgressProps {
  currentStep: number;
  totalSteps: number;
  steps: string[];
}

export function BookingProgress({ currentStep, totalSteps, steps }: BookingProgressProps) {
  return (
    <div className="w-full py-8">
      <div className="flex items-center justify-between relative">
        {/* Progress line */}
        <div className="absolute top-6 left-0 right-0 h-0.5 bg-booking-step-inactive">
          <div
            className="h-full bg-booking-step transition-all duration-500 ease-out"
            style={{ width: `${((currentStep - 1) / (totalSteps - 1)) * 100}%` }}
          />
        </div>

        {/* Steps */}
        {steps.map((step, index) => {
          const stepNumber = index + 1;
          const isComplete = stepNumber < currentStep;
          const isCurrent = stepNumber === currentStep;
          const isInactive = stepNumber > currentStep;

          return (
            <div key={index} className="flex flex-col items-center relative z-10">
              <div
                className={cn(
                  "w-12 h-12 rounded-full flex items-center justify-center text-sm font-semibold transition-all duration-300",
                  {
                    "bg-booking-step text-white shadow-lg": isCurrent,
                    "bg-booking-step-complete text-white": isComplete,
                    "bg-booking-step-inactive text-muted-foreground": isInactive,
                  }
                )}
              >
                {isComplete ? (
                  <Check className="w-5 h-5" />
                ) : (
                  <span>{stepNumber}</span>
                )}
              </div>
              <span
                className={cn(
                  "mt-2 text-xs font-medium text-center max-w-20 transition-colors duration-300",
                  {
                    "text-booking-step": isCurrent,
                    "text-booking-step-complete": isComplete,
                    "text-muted-foreground": isInactive,
                  }
                )}
              >
                {step}
              </span>
            </div>
          );
        })}
      </div>
    </div>
  );
}